

<?php $__env->startSection('title', 'Dashboard'); ?>

<?php $__env->startSection('page_title', 'Dashboard Website'); ?>

<?php $__env->startPush('styles'); ?>
<style>
.stats-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    border: none;
    border-radius: 10px;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stats-card .card-body {
    padding: 1.5rem;
}

.stats-card .icon {
    opacity: 0.8;
}

.stats-card h3 {
    font-size: 2rem;
    font-weight: bold;
    margin: 0.5rem 0;
}

.stats-card p {
    font-size: 0.9rem;
    margin: 0;
    opacity: 0.9;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.bg-gradient-success {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.bg-gradient-info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.bg-gradient-warning {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    color: white;
}

.bg-gradient-secondary {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    color: white;
}

.bg-gradient-dark {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    color: #333;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<!-- Tambahkan card ucapan selamat datang -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card bg-gradient-info">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-hand-wave mr-2"></i>
                    Selamat Datang di SIMAS Pelopor, <?php echo e(Auth::user()->name); ?>!
                </h5>
                <p class="card-text">
                    Sistem Informasi Manajemen Akademik Sekolah (SIMAS) Pelopor - Platform Digital Terpadu untuk Pengelolaan Akademik
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Statistik Website -->
<div class="row mb-4">
    <div class="col-12">
        <h4 class="mb-3">
            <i class="fas fa-globe mr-2"></i>
            Statistik Konten Website
        </h4>
        <p class="text-muted mb-0">Ringkasan data konten website sekolah yang telah dipublikasikan</p>
    </div>
</div>

<!-- Baris Pertama: 3 Card -->
<div class="row mb-4">
    <!-- Card Event -->
    <div class="col-lg-4 col-md-6 col-sm-12 mb-3">
        <div class="card stats-card bg-gradient-primary h-100">
            <div class="card-body text-center">
                <div class="icon mb-3">
                    <i class="fas fa-calendar-alt fa-3x"></i>
                </div>
                <h2 class="card-title mb-2"><?php echo e($totalEvents ?? 0); ?></h2>
                <p class="card-text mb-1"><strong>Kegiatan & Event</strong></p>
                <small class="text-light opacity-75">Total kegiatan sekolah yang terdaftar</small>
            </div>
        </div>
    </div>

    <!-- Card Prestasi -->
    <div class="col-lg-4 col-md-6 col-sm-12 mb-3">
        <div class="card stats-card bg-gradient-success h-100">
            <div class="card-body text-center">
                <div class="icon mb-3">
                    <i class="fas fa-trophy fa-3x"></i>
                </div>
                <h2 class="card-title mb-2"><?php echo e($totalAchievements ?? 0); ?></h2>
                <p class="card-text mb-1"><strong>Prestasi & Penghargaan</strong></p>
                <small class="text-light opacity-75">Pencapaian siswa dan sekolah</small>
            </div>
        </div>
    </div>

    <!-- Card Fasilitas -->
    <div class="col-lg-4 col-md-6 col-sm-12 mb-3">
        <div class="card stats-card bg-gradient-info h-100">
            <div class="card-body text-center">
                <div class="icon mb-3">
                    <i class="fas fa-building fa-3x"></i>
                </div>
                <h2 class="card-title mb-2"><?php echo e($totalFacilities ?? 0); ?></h2>
                <p class="card-text mb-1"><strong>Fasilitas Sekolah</strong></p>
                <small class="text-light opacity-75">Sarana dan prasarana tersedia</small>
            </div>
        </div>
    </div>
</div>

<!-- Baris Kedua: 3 Card -->
<div class="row mb-4">
    <!-- Card Ekstrakurikuler -->
    <div class="col-lg-4 col-md-6 col-sm-12 mb-3">
        <div class="card stats-card bg-gradient-warning h-100">
            <div class="card-body text-center">
                <div class="icon mb-3">
                    <i class="fas fa-users fa-3x"></i>
                </div>
                <h2 class="card-title mb-2"><?php echo e($totalExtracurriculars ?? 0); ?></h2>
                <p class="card-text mb-1"><strong>Ekstrakurikuler</strong></p>
                <small class="text-light opacity-75">Kegiatan pengembangan bakat siswa</small>
            </div>
        </div>
    </div>

    <!-- Card Slide -->
    <div class="col-lg-4 col-md-6 col-sm-12 mb-3">
        <div class="card stats-card bg-gradient-secondary h-100">
            <div class="card-body text-center">
                <div class="icon mb-3">
                    <i class="fas fa-images fa-3x"></i>
                </div>
                <h2 class="card-title mb-2"><?php echo e($totalSlides ?? 0); ?></h2>
                <p class="card-text mb-1"><strong>Slide Banner</strong></p>
                <small class="text-light opacity-75">Gambar promosi di halaman utama</small>
            </div>
        </div>
    </div>

    <!-- Card Unit -->
    <div class="col-lg-4 col-md-6 col-sm-12 mb-3">
        <div class="card stats-card bg-gradient-dark h-100">
            <div class="card-body text-center">
                <div class="icon mb-3">
                    <i class="fas fa-school fa-3x"></i>
                </div>
                <h2 class="card-title mb-2"><?php echo e(isset($units) ? $units->count() : 1); ?></h2>
                <p class="card-text mb-1"><strong>Unit Sekolah</strong></p>
                <small class="opacity-75">Jenjang pendidikan yang tersedia</small>
            </div>
        </div>
    </div>
</div>

<?php if (\Illuminate\Support\Facades\Blade::check('role', 'Administrator|Yayasan|Pengawas')): ?>
<!-- Statistik Per Unit (Hanya untuk Admin) -->
<?php if(isset($eventsPerUnit) || isset($achievementsPerUnit) || isset($extracurricularsPerUnit)): ?>
<div class="row mb-4">
    <div class="col-12">
        <h4 class="mb-3">
            <i class="fas fa-chart-bar mr-2"></i>
            Statistik Website Per Unit
        </h4>
    </div>
</div>

<div class="row">
    <!-- Event Per Unit -->
    <?php if(isset($eventsPerUnit) && $eventsPerUnit->count() > 0): ?>
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-alt mr-2"></i>
                    Event Per Unit
                </h5>
            </div>
            <div class="card-body">
                <?php $__currentLoopData = $eventsPerUnit; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="text-sm"><?php echo e($item['unit']); ?></span>
                    <span class="badge badge-primary"><?php echo e($item['total']); ?></span>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Prestasi Per Unit -->
    <?php if(isset($achievementsPerUnit) && $achievementsPerUnit->count() > 0): ?>
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-trophy mr-2"></i>
                    Prestasi Per Unit
                </h5>
            </div>
            <div class="card-body">
                <?php $__currentLoopData = $achievementsPerUnit; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="text-sm"><?php echo e($item['unit']); ?></span>
                    <span class="badge badge-success"><?php echo e($item['total']); ?></span>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Ekstrakurikuler Per Unit -->
    <?php if(isset($extracurricularsPerUnit) && $extracurricularsPerUnit->count() > 0): ?>
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-users mr-2"></i>
                    Ekstrakurikuler Per Unit
                </h5>
            </div>
            <div class="card-body">
                <?php $__currentLoopData = $extracurricularsPerUnit; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="text-sm"><?php echo e($item['unit']); ?></span>
                    <span class="badge badge-warning"><?php echo e($item['total']); ?></span>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>
<?php endif; ?>
<?php endif; ?>

<!-- Statistik Utama -->

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\webplp\resources\views/admin/dashboard.blade.php ENDPATH**/ ?>