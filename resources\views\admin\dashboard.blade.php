@extends('layouts.admin')

@section('title', 'Dashboard')

@section('page_title', 'Dashboard Website')

@push('styles')
<style>
.stats-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    border: none;
    border-radius: 10px;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stats-card .card-body {
    padding: 1.5rem;
}

.stats-card .icon {
    opacity: 0.8;
}

.stats-card h3 {
    font-size: 2rem;
    font-weight: bold;
    margin: 0.5rem 0;
}

.stats-card p {
    font-size: 0.9rem;
    margin: 0;
    opacity: 0.9;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.bg-gradient-success {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.bg-gradient-info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.bg-gradient-warning {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    color: white;
}

.bg-gradient-secondary {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    color: white;
}

.bg-gradient-dark {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    color: #333;
}
</style>
@endpush

@section('content')
<!-- Tambahkan card ucapan selamat datang -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card bg-gradient-info">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-hand-wave mr-2"></i>
                    Selamat Datang di SIMAS Pelopor, {{ Auth::user()->name }}!
                </h5>
                <p class="card-text">
                    Sistem Informasi Manajemen Akademik Sekolah (SIMAS) Pelopor - Platform Digital Terpadu untuk Pengelolaan Akademik
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Statistik Website -->
<div class="row mb-4">
    <div class="col-12">
        <h4 class="mb-3">
            <i class="fas fa-globe mr-2"></i>
            Statistik Website
        </h4>
    </div>
</div>

<div class="row mb-4">
    <!-- Event Card -->
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card stats-card bg-gradient-primary h-100">
            <div class="card-body text-center">
                <div class="icon mb-2">
                    <i class="fas fa-calendar-alt fa-2x"></i>
                </div>
                <h3 class="card-title">{{ $totalEvents ?? 0 }}</h3>
                <p class="card-text">Event</p>
            </div>
        </div>
    </div>

    <!-- Prestasi Card -->
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card stats-card bg-gradient-success h-100">
            <div class="card-body text-center">
                <div class="icon mb-2">
                    <i class="fas fa-trophy fa-2x"></i>
                </div>
                <h3 class="card-title">{{ $totalAchievements ?? 0 }}</h3>
                <p class="card-text">Prestasi</p>
            </div>
        </div>
    </div>

    <!-- Fasilitas Card -->
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card stats-card bg-gradient-info h-100">
            <div class="card-body text-center">
                <div class="icon mb-2">
                    <i class="fas fa-building fa-2x"></i>
                </div>
                <h3 class="card-title">{{ $totalFacilities ?? 0 }}</h3>
                <p class="card-text">Fasilitas</p>
            </div>
        </div>
    </div>

    <!-- Ekstrakurikuler Card -->
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card stats-card bg-gradient-warning h-100">
            <div class="card-body text-center">
                <div class="icon mb-2">
                    <i class="fas fa-users fa-2x"></i>
                </div>
                <h3 class="card-title">{{ $totalExtracurriculars ?? 0 }}</h3>
                <p class="card-text">Ekstrakurikuler</p>
            </div>
        </div>
    </div>

    <!-- Slide Card -->
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card stats-card bg-gradient-secondary h-100">
            <div class="card-body text-center">
                <div class="icon mb-2">
                    <i class="fas fa-images fa-2x"></i>
                </div>
                <h3 class="card-title">{{ $totalSlides ?? 0 }}</h3>
                <p class="card-text">Slide Aktif</p>
            </div>
        </div>
    </div>

    <!-- Unit Card -->
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="card stats-card bg-gradient-dark h-100">
            <div class="card-body text-center">
                <div class="icon mb-2">
                    <i class="fas fa-school fa-2x"></i>
                </div>
                <h3 class="card-title">{{ isset($units) ? $units->count() : 1 }}</h3>
                <p class="card-text">Unit</p>
            </div>
        </div>
    </div>
</div>

@role('Administrator|Yayasan|Pengawas')
<!-- Statistik Per Unit (Hanya untuk Admin) -->
@if(isset($eventsPerUnit) || isset($achievementsPerUnit) || isset($extracurricularsPerUnit))
<div class="row mb-4">
    <div class="col-12">
        <h4 class="mb-3">
            <i class="fas fa-chart-bar mr-2"></i>
            Statistik Website Per Unit
        </h4>
    </div>
</div>

<div class="row">
    <!-- Event Per Unit -->
    @if(isset($eventsPerUnit) && $eventsPerUnit->count() > 0)
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-alt mr-2"></i>
                    Event Per Unit
                </h5>
            </div>
            <div class="card-body">
                @foreach($eventsPerUnit as $item)
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="text-sm">{{ $item['unit'] }}</span>
                    <span class="badge badge-primary">{{ $item['total'] }}</span>
                </div>
                @endforeach
            </div>
        </div>
    </div>
    @endif

    <!-- Prestasi Per Unit -->
    @if(isset($achievementsPerUnit) && $achievementsPerUnit->count() > 0)
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-trophy mr-2"></i>
                    Prestasi Per Unit
                </h5>
            </div>
            <div class="card-body">
                @foreach($achievementsPerUnit as $item)
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="text-sm">{{ $item['unit'] }}</span>
                    <span class="badge badge-success">{{ $item['total'] }}</span>
                </div>
                @endforeach
            </div>
        </div>
    </div>
    @endif

    <!-- Ekstrakurikuler Per Unit -->
    @if(isset($extracurricularsPerUnit) && $extracurricularsPerUnit->count() > 0)
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-users mr-2"></i>
                    Ekstrakurikuler Per Unit
                </h5>
            </div>
            <div class="card-body">
                @foreach($extracurricularsPerUnit as $item)
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="text-sm">{{ $item['unit'] }}</span>
                    <span class="badge badge-warning">{{ $item['total'] }}</span>
                </div>
                @endforeach
            </div>
        </div>
    </div>
    @endif
</div>
@endif
@endrole

<!-- Statistik Utama -->
