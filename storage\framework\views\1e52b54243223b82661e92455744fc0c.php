

<?php $__env->startSection('title', 'Data Peserta Didik Aktif'); ?>

<?php $__env->startSection('content_header'); ?>
    <h1>Data Peserta Didik Aktif</h1>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Data Peserta Didik Aktif</h3>
                        <div class="card-tools">
                            <div class="d-flex">
                                <?php if(auth()->user()->hasAnyRole(['Administrator', 'Yayasan'])): ?>
                                <select id="filter-unit" class="form-control mr-2">
                                    <option value="">Semua Unit</option>
                                    <?php $__currentLoopData = \App\Models\Unit::orderBy('nama_unit')->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $unit): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($unit->nama_unit); ?>"><?php echo e($unit->nama_unit); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <?php endif; ?>
                                <!-- Tombol mutasi keluar -->
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage-peserta-didik')): ?>
                                <button class="btn btn-warning mr-2" data-toggle="modal" data-target="#mutasiKeluarModal" style="white-space: nowrap;">
                                    <i class="fas fa-exchange-alt"></i> Mutasi Keluar
                                </button>
                                <!-- Tombol import/export -->
                                <div class="btn-group mr-2">
                                    <button type="button" class="btn btn-success dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                                        <i class="fas fa-file-excel"></i> Import/Export
                                    </button>
                                    <div class="dropdown-menu">
                                        <a class="dropdown-item" href="#" data-toggle="modal" data-target="#importModal">
                                            <i class="fas fa-file-import"></i> Import Data
                                        </a>
                                        <a class="dropdown-item" href="<?php echo e(route('peserta-didik.export-template')); ?>">
                                            <i class="fas fa-download"></i> Download Template
                                        </a>
                                        <a class="dropdown-item" href="<?php echo e(route('peserta-didik.export')); ?>">
                                            <i class="fas fa-file-export"></i> Export Data
                                        </a>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#tambahSiswa" style="white-space: nowrap;">
                                    <i class="fas fa-plus"></i> Tambah PD Baru
                                </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if(session('success')): ?>
                            <div class="alert alert-success">
                                <?php echo e(session('success')); ?>

                            </div>
                        <?php endif; ?>
                        
                        <table id="tabel-siswa" class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>NIS</th>
                                    <th>NISN</th>
                                    <th>Nama</th>
                                    <th>Jenis Kelamin</th>
                                    <th>Unit</th>
                                    <th>Kelas</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $siswa; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $s): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e($s->nis); ?></td>
                                    <td><?php echo e($s->nisn); ?></td>
                                    <td><?php echo e($s->nama); ?></td>
                                    <td><?php echo e($s->jenis_kelamin == 'L' ? 'Laki-laki' : 'Perempuan'); ?></td>
                                    <td><?php echo e($s->unit->nama_unit ?? 'Belum ada unit'); ?></td>
                                    <td><?php echo e($s->kelas->nama ?? 'Belum ada kelas'); ?></td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" data-toggle="modal" data-target="#detailSiswa<?php echo e($s->id); ?>" data-toggle="tooltip" title="Detail">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage-peserta-didik')): ?>
                                        <button class="btn btn-sm btn-info" data-toggle="modal" data-target="#editSiswa<?php echo e($s->id); ?>" data-toggle="tooltip" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <form action="<?php echo e(route('peserta-didik.destroy', $s->id)); ?>" method="POST" class="d-inline">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Yakin ingin menghapus data ini?')" data-toggle="tooltip" title="Hapus">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                        <!--Tobol mutasi keluar 
                                        <button class="btn btn-warning" data-toggle="modal" data-target="#mutasiKeluarModal">
                                            <i class="fas fa-exchange-alt"></i> Mutasi Keluar
                                        </button> -->
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php echo $__env->make('peserta-didik.modals.detail', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage-peserta-didik')): ?>
        <?php echo $__env->make('peserta-didik.modals.create', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <?php echo $__env->make('peserta-didik.modals.edit', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <?php echo $__env->make('peserta-didik.modals.import', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php endif; ?>
    <!-- Include modal mutasi keluar -->
    <?php echo $__env->make('rombel.modals.mutasi-keluar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('css'); ?>
    <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap4.min.css">
<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
    <script src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.24/js/dataTables.bootstrap4.min.js"></script>
    <script>
        $(document).ready(function() {
            var table = $('#tabel-siswa').DataTable({
                "responsive": true,
                "lengthChange": true,
                "autoWidth": false,
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
                }
            });

            // Filter berdasarkan unit (hanya jika elemen filter-unit ada)
            if ($('#filter-unit').length > 0) {
                $('#filter-unit').on('change', function() {
                    var unit = $(this).val();
                    table.column(4) // Kolom unit (indeks 4)
                        .search(unit)
                        .draw();
                });
            }
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('adminlte::page', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\webplp\resources\views/peserta-didik/aktif.blade.php ENDPATH**/ ?>